import React from 'react';
import { useNavigate } from 'react-router-dom';

const About = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  const handleGoHome = () => {
    navigate('/'); // Navigate to home page
  };

  return (
    <div className="page-container">
      <div className="content-section">
        <h1>About Us</h1>
        <div className="about-content">
          <p>
            This React application demonstrates the power of browser routing using React Router DOM.
            Here are some key features showcased:
          </p>
          
          <div className="info-grid">
            <div className="info-card">
              <h3>🔗 Browser Router</h3>
              <p>Uses HTML5 history API for clean URLs without hash symbols.</p>
            </div>
            
            <div className="info-card">
              <h3>🛣️ Route Configuration</h3>
              <p>Declarative routing with nested route support.</p>
            </div>
            
            <div className="info-card">
              <h3>🧭 Navigation</h3>
              <p>Programmatic navigation using useNavigate hook.</p>
            </div>
            
            <div className="info-card">
              <h3>🔍 Dynamic Routes</h3>
              <p>Support for URL parameters and query strings.</p>
            </div>
          </div>

          <div className="navigation-demo">
            <h3>Navigation Demo</h3>
            <p>Try these programmatic navigation buttons:</p>
            <div className="button-group">
              <button onClick={handleGoBack} className="nav-button">
                ← Go Back
              </button>
              <button onClick={handleGoHome} className="nav-button">
                🏠 Go Home
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
