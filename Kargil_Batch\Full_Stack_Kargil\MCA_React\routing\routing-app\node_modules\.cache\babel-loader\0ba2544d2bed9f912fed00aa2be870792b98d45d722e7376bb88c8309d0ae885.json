{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kargil_Batch\\\\Full_Stack_Kargil\\\\MCA_React\\\\routing\\\\routing-app\\\\src\\\\components\\\\NotFound.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  _s();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"not-found-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"error-code\",\n          children: \"404\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"error-title\",\n          children: \"Page Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"error-message\",\n          children: \"Oops! The page you're looking for doesn't exist.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Requested URL:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 16\n            }, this), \" \", location.pathname]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), location.search && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Query Parameters:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 18\n            }, this), \" \", location.search]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"home-button\",\n            children: \"\\uD83C\\uDFE0 Go Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.history.back(),\n            className: \"back-button\",\n            children: \"\\u2190 Go Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"suggestions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"You might be looking for:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"suggestion-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                children: \"Home Page\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                children: \"About Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/services\",\n                children: \"Our Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-illustration\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"illustration\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"planet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"astronaut\",\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Lost in space? Let's get you back on track!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(NotFound, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "NotFound", "_s", "location", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pathname", "search", "to", "onClick", "window", "history", "back", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Karg<PERSON>_<PERSON>ch/Full_Stack_Kargil/MCA_React/routing/routing-app/src/components/NotFound.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst NotFound = () => {\n  const location = useLocation();\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"not-found-container\">\n        <div className=\"error-content\">\n          <h1 className=\"error-code\">404</h1>\n          <h2 className=\"error-title\">Page Not Found</h2>\n          <p className=\"error-message\">\n            Oops! The page you're looking for doesn't exist.\n          </p>\n          \n          <div className=\"error-details\">\n            <p><strong>Requested URL:</strong> {location.pathname}</p>\n            {location.search && (\n              <p><strong>Query Parameters:</strong> {location.search}</p>\n            )}\n          </div>\n\n          <div className=\"error-actions\">\n            <Link to=\"/\" className=\"home-button\">\n              🏠 Go Home\n            </Link>\n            <button \n              onClick={() => window.history.back()} \n              className=\"back-button\"\n            >\n              ← Go Back\n            </button>\n          </div>\n\n          <div className=\"suggestions\">\n            <h3>You might be looking for:</h3>\n            <ul className=\"suggestion-list\">\n              <li><Link to=\"/\">Home Page</Link></li>\n              <li><Link to=\"/about\">About Us</Link></li>\n              <li><Link to=\"/services\">Our Services</Link></li>\n              <li><Link to=\"/contact\">Contact Us</Link></li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"error-illustration\">\n          <div className=\"illustration\">\n            <div className=\"planet\"></div>\n            <div className=\"astronaut\">🚀</div>\n            <p>Lost in space? Let's get you back on track!</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotFound;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,oBACEE,OAAA;IAAKI,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BL,OAAA;MAAKI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCL,OAAA;QAAKI,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BL,OAAA;UAAII,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCT,OAAA;UAAII,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CT,OAAA;UAAGI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJT,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BL,OAAA;YAAAK,QAAA,gBAAGL,OAAA;cAAAK,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACN,QAAQ,CAACO,QAAQ;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACzDN,QAAQ,CAACQ,MAAM,iBACdX,OAAA;YAAAK,QAAA,gBAAGL,OAAA;cAAAK,QAAA,EAAQ;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACN,QAAQ,CAACQ,MAAM;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENT,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BL,OAAA,CAACH,IAAI;YAACe,EAAE,EAAC,GAAG;YAACR,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA;YACEa,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;YACrCZ,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENT,OAAA;UAAKI,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BL,OAAA;YAAAK,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCT,OAAA;YAAII,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC7BL,OAAA;cAAAK,QAAA,eAAIL,OAAA,CAACH,IAAI;gBAACe,EAAE,EAAC,GAAG;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtCT,OAAA;cAAAK,QAAA,eAAIL,OAAA,CAACH,IAAI;gBAACe,EAAE,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1CT,OAAA;cAAAK,QAAA,eAAIL,OAAA,CAACH,IAAI;gBAACe,EAAE,EAAC,WAAW;gBAAAP,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDT,OAAA;cAAAK,QAAA,eAAIL,OAAA,CAACH,IAAI;gBAACe,EAAE,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENT,OAAA;QAAKI,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCL,OAAA;UAAKI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BL,OAAA;YAAKI,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9BT,OAAA;YAAKI,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCT,OAAA;YAAAK,QAAA,EAAG;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CArDID,QAAQ;EAAA,QACKH,WAAW;AAAA;AAAAmB,EAAA,GADxBhB,QAAQ;AAuDd,eAAeA,QAAQ;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}