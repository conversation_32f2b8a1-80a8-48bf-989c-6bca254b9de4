{"version": 3, "file": "SetLike.js", "names": ["SetLike", "items", "arguments", "length", "undefined", "_classCallCheck", "_defineProperty", "_createClass", "key", "value", "add", "has", "push", "clear", "_delete", "<PERSON><PERSON><PERSON><PERSON>", "filter", "item", "for<PERSON>ach", "callbackfn", "_this", "indexOf", "get", "_default", "Set", "exports", "default"], "sources": ["../../sources/polyfills/SetLike.ts"], "sourcesContent": ["declare global {\n\tclass Set<T> {\n\t\t// es2015.collection.d.ts\n\t\tconstructor(items?: T[]);\n\t\tadd(value: T): this;\n\t\tclear(): void;\n\t\tdelete(value: T): boolean;\n\t\tforEach(\n\t\t\tcallbackfn: (value: T, value2: T, set: Set<T>) => void,\n\t\t\tthisArg?: unknown,\n\t\t): void;\n\t\thas(value: T): boolean;\n\t\treadonly size: number;\n\n\t\t// es2015.iterable.d.ts\n\t\t// no  implemennted\n\t}\n}\n\n// for environments without Set we fallback to arrays with unique members\nclass SetLike<T> implements Set<T> {\n\tprivate items: T[];\n\n\tconstructor(items: T[] = []) {\n\t\tthis.items = items;\n\t}\n\n\tadd(value: T): this {\n\t\tif (this.has(value) === false) {\n\t\t\tthis.items.push(value);\n\t\t}\n\t\treturn this;\n\t}\n\tclear(): void {\n\t\tthis.items = [];\n\t}\n\tdelete(value: T): boolean {\n\t\tconst previousLength = this.items.length;\n\t\tthis.items = this.items.filter((item) => item !== value);\n\n\t\treturn previousLength !== this.items.length;\n\t}\n\tforEach(callbackfn: (value: T, value2: T, set: Set<T>) => void): void {\n\t\tthis.items.forEach((item) => {\n\t\t\tcallbackfn(item, item, this);\n\t\t});\n\t}\n\thas(value: T): boolean {\n\t\treturn this.items.indexOf(value) !== -1;\n\t}\n\n\tget size(): number {\n\t\treturn this.items.length;\n\t}\n}\n\nexport default typeof Set === \"undefined\" ? Set : SetLike;\n"], "mappings": ";;;;;;;;;;;AAmBA;AAAA,IACMA,OAAO;EAGZ,SAAAA,QAAA,EAA6B;IAAA,IAAjBC,KAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAAAG,eAAA,OAAAL,OAAA;IAAAM,eAAA;IAC1B,IAAI,CAACL,KAAK,GAAGA,KAAK;EACnB;EAACM,YAAA,CAAAP,OAAA;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAAC,IAAID,KAAQ,EAAQ;MACnB,IAAI,IAAI,CAACE,GAAG,CAACF,KAAK,CAAC,KAAK,KAAK,EAAE;QAC9B,IAAI,CAACR,KAAK,CAACW,IAAI,CAACH,KAAK,CAAC;MACvB;MACA,OAAO,IAAI;IACZ;EAAC;IAAAD,GAAA;IAAAC,KAAA,EACD,SAAAI,MAAA,EAAc;MACb,IAAI,CAACZ,KAAK,GAAG,EAAE;IAChB;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAK,QAAOL,KAAQ,EAAW;MACzB,IAAMM,cAAc,GAAG,IAAI,CAACd,KAAK,CAACE,MAAM;MACxC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACA,KAAK,CAACe,MAAM,CAAC,UAACC,IAAI;QAAA,OAAKA,IAAI,KAAKR,KAAK;MAAA,EAAC;MAExD,OAAOM,cAAc,KAAK,IAAI,CAACd,KAAK,CAACE,MAAM;IAC5C;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAS,QAAQC,UAAsD,EAAQ;MAAA,IAAAC,KAAA;MACrE,IAAI,CAACnB,KAAK,CAACiB,OAAO,CAAC,UAACD,IAAI,EAAK;QAC5BE,UAAU,CAACF,IAAI,EAAEA,IAAI,EAAEG,KAAI,CAAC;MAC7B,CAAC,CAAC;IACH;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EACD,SAAAE,IAAIF,KAAQ,EAAW;MACtB,OAAO,IAAI,CAACR,KAAK,CAACoB,OAAO,CAACZ,KAAK,CAAC,KAAK,CAAC,CAAC;IACxC;EAAC;IAAAD,GAAA;IAAAc,GAAA,EAED,SAAAA,IAAA,EAAmB;MAClB,OAAO,IAAI,CAACrB,KAAK,CAACE,MAAM;IACzB;EAAC;EAAA,OAAAH,OAAA;AAAA;AAAA,IAAAuB,QAAA,GAGa,OAAOC,GAAG,KAAK,WAAW,GAAGA,GAAG,GAAGxB,OAAO;AAAAyB,OAAA,CAAAC,OAAA,GAAAH,QAAA"}