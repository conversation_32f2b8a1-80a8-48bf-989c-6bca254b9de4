/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.App {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Navigation Styles */
.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-item {
  position: relative;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 10px 15px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Main Content */
.main-content {
  min-height: calc(100vh - 70px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Page Container */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 60px 0;
}

.hero-section h1 {
  font-size: 3rem;
  color: #333;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-text {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto 40px;
  line-height: 1.6;
}

/* Features Grid */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.feature-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* Content Section */
.content-section {
  padding: 20px 0;
}

.content-section h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 1.1rem;
  color: #666;
  text-align: center;
  max-width: 600px;
  margin: 0 auto 40px;
  line-height: 1.6;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin: 40px 0;
}

.info-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.info-card:hover {
  transform: translateY(-3px);
}

.info-card h3 {
  color: #333;
  margin-bottom: 12px;
  font-size: 1.2rem;
}

.info-card p {
  color: #666;
  line-height: 1.5;
}

/* Navigation Demo */
.navigation-demo {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-top: 40px;
  text-align: center;
}

.navigation-demo h3 {
  color: #333;
  margin-bottom: 15px;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 20px;
}

.nav-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Location Info */
.location-info {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.location-info h3 {
  color: #333;
  margin-bottom: 15px;
}

.location-info p {
  color: #666;
  margin-bottom: 8px;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin: 40px 0;
}

.service-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.service-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.service-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.service-link {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.service-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Routing Demo */
.routing-demo {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-top: 40px;
  text-align: center;
}

.routing-demo h3 {
  color: #333;
  margin-bottom: 15px;
}

.demo-links {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 20px;
}

.demo-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.demo-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Search Params Demo */
.search-params-demo {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.search-params-demo h3 {
  color: #333;
  margin-bottom: 15px;
}

.param-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.param-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.param-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.param-button.clear {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Contact Form */
.contact-form-container {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin: 30px 0;
}

.contact-form h3 {
  color: #333;
  margin-bottom: 25px;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Contact Info */
.contact-info {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.contact-info h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.contact-methods {
  display: grid;
  gap: 15px;
}

.contact-method {
  color: #666;
  font-size: 1.1rem;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-method:last-child {
  border-bottom: none;
}

/* 404 Not Found Styles */
.not-found-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
  min-height: 60vh;
}

.error-content {
  text-align: center;
}

.error-code {
  font-size: 8rem;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 20px;
  line-height: 1;
}

.error-title {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 15px;
}

.error-message {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 30px;
}

.error-details {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.error-details p {
  color: #666;
  margin-bottom: 8px;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.home-button,
.back-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.home-button:hover,
.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.suggestions {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.suggestions h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.suggestion-list {
  list-style: none;
  display: grid;
  gap: 10px;
}

.suggestion-list li {
  text-align: center;
}

.suggestion-list a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.suggestion-list a:hover {
  color: #764ba2;
}

/* Error Illustration */
.error-illustration {
  display: flex;
  justify-content: center;
  align-items: center;
}

.illustration {
  text-align: center;
  position: relative;
}

.planet {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin: 0 auto 20px;
  position: relative;
  animation: float 3s ease-in-out infinite;
}

.astronaut {
  font-size: 3rem;
  position: absolute;
  top: -20px;
  right: -20px;
  animation: bounce 2s ease-in-out infinite;
}

.illustration p {
  color: #666;
  font-style: italic;
  margin-top: 20px;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(10deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    height: auto;
    padding: 15px 20px;
  }

  .nav-menu {
    margin-top: 15px;
    gap: 15px;
  }

  .hero-section h1 {
    font-size: 2rem;
  }

  .features {
    grid-template-columns: 1fr;
  }

  .not-found-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .error-code {
    font-size: 5rem;
  }

  .error-title {
    font-size: 2rem;
  }

  .button-group,
  .demo-links,
  .param-controls,
  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .planet {
    width: 150px;
    height: 150px;
  }

  .astronaut {
    font-size: 2rem;
  }
}

/* User Profile Styles */
.user-profile-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

.profile-info h2 {
  color: #333;
  margin-bottom: 5px;
}

.user-role {
  color: #667eea;
  font-weight: 500;
  font-size: 1.1rem;
}

.profile-details {
  display: grid;
  gap: 15px;
}

.detail-item {
  color: #666;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.user-links {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.user-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.user-link:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.user-link.error-link {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.params-info {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.params-info h3 {
  color: #333;
  margin-bottom: 15px;
}

.params-display {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.params-display p {
  color: #666;
  margin-bottom: 8px;
}

.params-display p:last-child {
  margin-bottom: 0;
}

/* Product Styles */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin: 40px 0;
}

.category-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.category-card h3 {
  color: #333;
  margin-bottom: 10px;
}

.category-card p {
  color: #666;
  margin-bottom: 20px;
}

.category-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
}

.category-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin: 40px 0;
}

.product-card-small {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.product-card-small:hover {
  transform: translateY(-3px);
}

.product-image-small {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 15px;
}

.product-info-small h3 {
  color: #333;
  margin-bottom: 8px;
}

.product-price {
  color: #667eea;
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.product-description-small {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.view-product-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: inline-block;
}

.view-product-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.breadcrumb-link {
  color: #667eea;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link:hover {
  color: #764ba2;
}

.breadcrumb-separator {
  color: #999;
}

.breadcrumb-current {
  color: #666;
  font-weight: 500;
}

/* Product Details */
.product-card {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 40px;
  margin-bottom: 30px;
}

.product-image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-image {
  font-size: 4rem;
  color: #ccc;
}

.product-info h2 {
  color: #333;
  margin-bottom: 15px;
  font-size: 2rem;
}

.product-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.product-meta {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.product-meta p {
  color: #666;
  margin-bottom: 8px;
}

.product-meta p:last-child {
  margin-bottom: 0;
}

.product-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.add-to-cart-btn,
.category-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.add-to-cart-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.category-btn {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.add-to-cart-btn:hover,
.category-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.category-btn:hover {
  background: #667eea;
  color: white;
}

/* Category Navigation */
.category-navigation {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.category-navigation h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.category-links {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.category-link {
  background: #f8f9fa;
  color: #667eea;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.category-link:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.category-link.active {
  background: #667eea;
  color: white;
  border-color: #764ba2;
}

.category-link.error-link {
  background: #ffe6e6;
  color: #ff6b6b;
}

.category-link.error-link:hover {
  background: #ff6b6b;
  color: white;
}

/* Related Products */
.related-products {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.related-products h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.product-links {
  display: grid;
  gap: 10px;
}

.product-link {
  background: #f8f9fa;
  color: #667eea;
  text-decoration: none;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.product-link:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.product-link.active {
  background: #667eea;
  color: white;
  border-color: #764ba2;
}

/* Test Links */
.test-404,
.demo-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.test-404 h3,
.demo-section h4 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.test-links {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.test-link {
  background: #ffe6e6;
  color: #ff6b6b;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-link:hover {
  background: #ff6b6b;
  color: white;
  transform: translateY(-1px);
}

.test-link.error-link {
  background: #ffe6e6;
  color: #ff6b6b;
}

/* Explanation Card */
.useParams-explanation {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin-top: 40px;
}

.useParams-explanation h3 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.explanation-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.explanation-card h4 {
  color: #333;
  margin-bottom: 15px;
}

.explanation-card ol {
  color: #666;
  line-height: 1.6;
}

.explanation-card li {
  margin-bottom: 8px;
}

.explanation-card strong {
  color: #333;
}
