import React from "react";
import { Link, useLocation } from "react-router-dom";

const NotFound = ({ customMessage }) => {
  const location = useLocation();

  return (
    <div className="page-container">
      <div className="not-found-container">
        <div className="error-content">
          <h1 className="error-code">404</h1>
          <h2 className="error-title">Page Not Found</h2>
          <p className="error-message">
            {customMessage ||
              "Oops! The page you're looking for doesn't exist."}
          </p>

          <div className="error-details">
            <p>
              <strong>Requested URL:</strong> {location.pathname}
            </p>
            {location.search && (
              <p>
                <strong>Query Parameters:</strong> {location.search}
              </p>
            )}
          </div>

          <div className="error-actions">
            <Link to="/" className="home-button">
              🏠 Go Home
            </Link>
            <button
              onClick={() => window.history.back()}
              className="back-button"
            >
              ← Go Back
            </button>
          </div>

          <div className="suggestions">
            <h3>You might be looking for:</h3>
            <ul className="suggestion-list">
              <li>
                <Link to="/">Home Page</Link>
              </li>
              <li>
                <Link to="/about">About Us</Link>
              </li>
              <li>
                <Link to="/services">Our Services</Link>
              </li>
              <li>
                <Link to="/contact">Contact Us</Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="error-illustration">
          <div className="illustration">
            <div className="planet"></div>
            <div className="astronaut">🚀</div>
            <p>Lost in space? Let's get you back on track!</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
