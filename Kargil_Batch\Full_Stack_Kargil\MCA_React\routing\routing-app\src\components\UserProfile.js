import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import NotFound from './NotFound';

const UserProfile = () => {
  const { userId } = useParams();
  
  // Mock user data - in a real app, this would come from an API
  const users = {
    '1': { id: 1, name: '<PERSON>', email: '<EMAIL>', role: '<PERSON><PERSON><PERSON>' },
    '2': { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Designer' },
    '3': { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Manager' },
    '4': { id: 4, name: '<PERSON>', email: '<EMAIL>', role: 'Tester' },
    '5': { id: 5, name: '<PERSON>', email: '<EMAIL>', role: '<PERSON>O<PERSON>' }
  };

  // Check if user exists - if not, render 404
  const user = users[userId];
  
  if (!user) {
    return <NotFound customMessage={`User with ID "${userId}" not found`} />;
  }

  return (
    <div className="page-container">
      <div className="content-section">
        <h1>User Profile</h1>
        <p className="page-description">
          Viewing profile for user ID: <strong>{userId}</strong>
        </p>

        <div className="user-profile-card">
          <div className="profile-header">
            <div className="profile-avatar">
              {user.name.split(' ').map(n => n[0]).join('')}
            </div>
            <div className="profile-info">
              <h2>{user.name}</h2>
              <p className="user-role">{user.role}</p>
            </div>
          </div>

          <div className="profile-details">
            <div className="detail-item">
              <strong>User ID:</strong> {user.id}
            </div>
            <div className="detail-item">
              <strong>Email:</strong> {user.email}
            </div>
            <div className="detail-item">
              <strong>Role:</strong> {user.role}
            </div>
          </div>
        </div>

        <div className="navigation-demo">
          <h3>Try Different User IDs</h3>
          <p>Click these links to see different user profiles or trigger 404 errors:</p>
          <div className="user-links">
            {Object.values(users).map(u => (
              <Link key={u.id} to={`/user/${u.id}`} className="user-link">
                User {u.id} ({u.name})
              </Link>
            ))}
            <Link to="/user/999" className="user-link error-link">
              User 999 (Non-existent - 404)
            </Link>
            <Link to="/user/abc" className="user-link error-link">
              User ABC (Invalid ID - 404)
            </Link>
          </div>
        </div>

        <div className="params-info">
          <h3>useParams() Information</h3>
          <div className="params-display">
            <p><strong>Current userId parameter:</strong> {userId}</p>
            <p><strong>Parameter type:</strong> {typeof userId}</p>
            <p><strong>Is valid user:</strong> {user ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
