import React from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import NotFound from './NotFound';

const ProductDetails = () => {
  const { category, productId } = useParams();
  const navigate = useNavigate();
  
  // Mock product data
  const products = {
    electronics: {
      '1': { id: 1, name: 'Smartphone', price: '$699', description: 'Latest smartphone with advanced features' },
      '2': { id: 2, name: '<PERSON>pt<PERSON>', price: '$1299', description: 'High-performance laptop for professionals' },
      '3': { id: 3, name: 'Headphones', price: '$199', description: 'Wireless noise-canceling headphones' }
    },
    clothing: {
      '1': { id: 1, name: 'T-Shirt', price: '$29', description: 'Comfortable cotton t-shirt' },
      '2': { id: 2, name: '<PERSON><PERSON>', price: '$79', description: 'Classic blue jeans' },
      '3': { id: 3, name: 'Sneakers', price: '$129', description: 'Comfortable running sneakers' }
    },
    books: {
      '1': { id: 1, name: 'React Guide', price: '$39', description: 'Complete guide to React development' },
      '2': { id: 2, name: 'JavaScript Mastery', price: '$45', description: 'Master JavaScript programming' },
      '3': { id: 3, name: 'Web Design', price: '$35', description: 'Modern web design principles' }
    }
  };

  const validCategories = Object.keys(products);
  
  // Check if category exists
  if (!validCategories.includes(category)) {
    return <NotFound customMessage={`Category "${category}" not found`} />;
  }
  
  // Check if product exists in the category
  const product = products[category][productId];
  if (!product) {
    return <NotFound customMessage={`Product with ID "${productId}" not found in category "${category}"`} />;
  }

  const handleGoToCategory = () => {
    navigate(`/products/${category}`);
  };

  return (
    <div className="page-container">
      <div className="content-section">
        <div className="breadcrumb">
          <Link to="/products" className="breadcrumb-link">Products</Link>
          <span className="breadcrumb-separator">›</span>
          <Link to={`/products/${category}`} className="breadcrumb-link">{category}</Link>
          <span className="breadcrumb-separator">›</span>
          <span className="breadcrumb-current">{product.name}</span>
        </div>

        <h1>Product Details</h1>
        
        <div className="product-card">
          <div className="product-image">
            <div className="placeholder-image">
              📦
            </div>
          </div>
          
          <div className="product-info">
            <h2>{product.name}</h2>
            <p className="product-price">{product.price}</p>
            <p className="product-description">{product.description}</p>
            
            <div className="product-meta">
              <p><strong>Category:</strong> {category}</p>
              <p><strong>Product ID:</strong> {productId}</p>
            </div>
            
            <div className="product-actions">
              <button className="add-to-cart-btn">Add to Cart</button>
              <button onClick={handleGoToCategory} className="category-btn">
                View All {category}
              </button>
            </div>
          </div>
        </div>

        <div className="params-demo">
          <h3>URL Parameters Demo</h3>
          <div className="params-display">
            <p><strong>Category parameter:</strong> {category}</p>
            <p><strong>Product ID parameter:</strong> {productId}</p>
            <p><strong>Full URL pattern:</strong> /products/:category/:productId</p>
          </div>
        </div>

        <div className="related-products">
          <h3>Other Products in {category}</h3>
          <div className="product-links">
            {Object.values(products[category]).map(p => (
              <Link 
                key={p.id} 
                to={`/products/${category}/${p.id}`} 
                className={`product-link ${p.id.toString() === productId ? 'active' : ''}`}
              >
                {p.name} - {p.price}
              </Link>
            ))}
          </div>
        </div>

        <div className="test-404">
          <h3>Test 404 Scenarios</h3>
          <div className="test-links">
            <Link to="/products/invalid-category/1" className="test-link error-link">
              Invalid Category
            </Link>
            <Link to={`/products/${category}/999`} className="test-link error-link">
              Invalid Product ID
            </Link>
            <Link to="/products/electronics/abc" className="test-link error-link">
              Non-numeric Product ID
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetails;
