{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kargil_Batch\\\\Full_Stack_Kargil\\\\MCA_React\\\\routing\\\\routing-app\\\\src\\\\components\\\\About.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const navigate = useNavigate();\n  const handleGoBack = () => {\n    navigate(-1); // Go back to previous page\n  };\n  const handleGoHome = () => {\n    navigate('/'); // Navigate to home page\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"About Us\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This React application demonstrates the power of browser routing using React Router DOM. Here are some key features showcased:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDD17 Browser Router\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Uses HTML5 history API for clean URLs without hash symbols.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDEE3\\uFE0F Route Configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Declarative routing with nested route support.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83E\\uDDED Navigation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Programmatic navigation using useNavigate hook.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDD0D Dynamic Routes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Support for URL parameters and query strings.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navigation-demo\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Navigation Demo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Try these programmatic navigation buttons:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"button-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoBack,\n              className: \"nav-button\",\n              children: \"\\u2190 Go Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoHome,\n              className: \"nav-button\",\n              children: \"\\uD83C\\uDFE0 Go Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "About", "_s", "navigate", "handleGoBack", "handleGoHome", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Karg<PERSON>_<PERSON>ch/Full_Stack_Kargil/MCA_React/routing/routing-app/src/components/About.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nconst About = () => {\n  const navigate = useNavigate();\n\n  const handleGoBack = () => {\n    navigate(-1); // Go back to previous page\n  };\n\n  const handleGoHome = () => {\n    navigate('/'); // Navigate to home page\n  };\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"content-section\">\n        <h1>About Us</h1>\n        <div className=\"about-content\">\n          <p>\n            This React application demonstrates the power of browser routing using React Router DOM.\n            Here are some key features showcased:\n          </p>\n          \n          <div className=\"info-grid\">\n            <div className=\"info-card\">\n              <h3>🔗 Browser Router</h3>\n              <p>Uses HTML5 history API for clean URLs without hash symbols.</p>\n            </div>\n            \n            <div className=\"info-card\">\n              <h3>🛣️ Route Configuration</h3>\n              <p>Declarative routing with nested route support.</p>\n            </div>\n            \n            <div className=\"info-card\">\n              <h3>🧭 Navigation</h3>\n              <p>Programmatic navigation using useNavigate hook.</p>\n            </div>\n            \n            <div className=\"info-card\">\n              <h3>🔍 Dynamic Routes</h3>\n              <p>Support for URL parameters and query strings.</p>\n            </div>\n          </div>\n\n          <div className=\"navigation-demo\">\n            <h3>Navigation Demo</h3>\n            <p>Try these programmatic navigation buttons:</p>\n            <div className=\"button-group\">\n              <button onClick={handleGoBack} className=\"nav-button\">\n                ← Go Back\n              </button>\n              <button onClick={handleGoHome} className=\"nav-button\">\n                🏠 Go Home\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBF,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EACjB,CAAC;EAED,oBACEH,OAAA;IAAKM,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BP,OAAA;MAAKM,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BP,OAAA;QAAAO,QAAA,EAAI;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBX,OAAA;QAAKM,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BP,OAAA;UAAAO,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJX,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBP,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBP,OAAA;cAAAO,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BX,OAAA;cAAAO,QAAA,EAAG;YAA2D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eAENX,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBP,OAAA;cAAAO,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCX,OAAA;cAAAO,QAAA,EAAG;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAENX,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBP,OAAA;cAAAO,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBX,OAAA;cAAAO,QAAA,EAAG;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAENX,OAAA;YAAKM,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBP,OAAA;cAAAO,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BX,OAAA;cAAAO,QAAA,EAAG;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENX,OAAA;UAAKM,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BP,OAAA;YAAAO,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBX,OAAA;YAAAO,QAAA,EAAG;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjDX,OAAA;YAAKM,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BP,OAAA;cAAQY,OAAO,EAAER,YAAa;cAACE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTX,OAAA;cAAQY,OAAO,EAAEP,YAAa;cAACC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CA3DID,KAAK;EAAA,QACQH,WAAW;AAAA;AAAAe,EAAA,GADxBZ,KAAK;AA6DX,eAAeA,KAAK;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}