{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kargil_Batch\\\\Full_Stack_Kargil\\\\MCA_React\\\\routing\\\\routing-app\\\\src\\\\App.js\";\nimport React from \"react\";\nimport { BrowserRouter as Router, Routes, Route, Link } from \"react-router-dom\";\nimport \"./App.css\";\n\n// Import components\nimport Home from \"./components/Home\";\nimport About from \"./components/About\";\nimport Contact from \"./components/Contact\";\nimport Services from \"./components/Services\";\nimport NotFound from \"./components/NotFound\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"navbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"nav-logo\",\n            children: \"React Router App\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/\",\n                className: \"nav-link\",\n                children: \"Home\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/about\",\n                className: \"nav-link\",\n                children: \"About\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/services\",\n                className: \"nav-link\",\n                children: \"Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/contact\",\n                className: \"nav-link\",\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/about\",\n            element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/services\",\n            element: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/contact\",\n            element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "Home", "About", "Contact", "Services", "NotFound", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Karg<PERSON>_<PERSON>ch/Full_Stack_Kargil/MCA_React/routing/routing-app/src/App.js"], "sourcesContent": ["import React from \"react\";\nimport { BrowserRouter as Router, Routes, Route, Link } from \"react-router-dom\";\nimport \"./App.css\";\n\n// Import components\nimport Home from \"./components/Home\";\nimport About from \"./components/About\";\nimport Contact from \"./components/Contact\";\nimport Services from \"./components/Services\";\nimport NotFound from \"./components/NotFound\";\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <nav className=\"navbar\">\n          <div className=\"nav-container\">\n            <h1 className=\"nav-logo\">React Router App</h1>\n            <ul className=\"nav-menu\">\n              <li className=\"nav-item\">\n                <Link to=\"/\" className=\"nav-link\">\n                  Home\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link to=\"/about\" className=\"nav-link\">\n                  About\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link to=\"/services\" className=\"nav-link\">\n                  Services\n                </Link>\n              </li>\n              <li className=\"nav-item\">\n                <Link to=\"/contact\" className=\"nav-link\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main className=\"main-content\">\n          <Routes>\n            <Route path=\"/\" element={<Home />} />\n            <Route path=\"/about\" element={<About />} />\n            <Route path=\"/services\" element={<Services />} />\n            <Route path=\"/contact\" element={<Contact />} />\n            <Route path=\"*\" element={<NotFound />} />\n          </Routes>\n        </main>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAO,WAAW;;AAElB;AACA,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,MAAM;IAAAY,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA;QAAKG,SAAS,EAAC,QAAQ;QAAAD,QAAA,eACrBF,OAAA;UAAKG,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC5BF,OAAA;YAAIG,SAAS,EAAC,UAAU;YAAAD,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CP,OAAA;YAAIG,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACtBF,OAAA;cAAIG,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBF,OAAA,CAACP,IAAI;gBAACe,EAAE,EAAC,GAAG;gBAACL,SAAS,EAAC,UAAU;gBAAAD,QAAA,EAAC;cAElC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAIG,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBF,OAAA,CAACP,IAAI;gBAACe,EAAE,EAAC,QAAQ;gBAACL,SAAS,EAAC,UAAU;gBAAAD,QAAA,EAAC;cAEvC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAIG,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBF,OAAA,CAACP,IAAI;gBAACe,EAAE,EAAC,WAAW;gBAACL,SAAS,EAAC,UAAU;gBAAAD,QAAA,EAAC;cAE1C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLP,OAAA;cAAIG,SAAS,EAAC,UAAU;cAAAD,QAAA,eACtBF,OAAA,CAACP,IAAI;gBAACe,EAAE,EAAC,UAAU;gBAACL,SAAS,EAAC,UAAU;gBAAAD,QAAA,EAAC;cAEzC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA;QAAMG,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC5BF,OAAA,CAACT,MAAM;UAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;YAACiB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEV,OAAA,CAACN,IAAI;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCP,OAAA,CAACR,KAAK;YAACiB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEV,OAAA,CAACL,KAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CP,OAAA,CAACR,KAAK;YAACiB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEV,OAAA,CAACH,QAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDP,OAAA,CAACR,KAAK;YAACiB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEV,OAAA,CAACJ,OAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CP,OAAA,CAACR,KAAK;YAACiB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEV,OAAA,CAACF,QAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACI,EAAA,GA5CQV,GAAG;AA8CZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}