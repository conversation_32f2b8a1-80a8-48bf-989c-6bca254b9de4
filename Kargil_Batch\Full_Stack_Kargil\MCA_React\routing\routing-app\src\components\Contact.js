import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const Contact = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  // Get query parameters
  const source = searchParams.get('source') || 'direct';
  const ref = searchParams.get('ref') || 'none';

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    alert(`Form submitted!\nName: ${formData.name}\nEmail: ${formData.email}\nMessage: ${formData.message}`);
    // Reset form
    setFormData({ name: '', email: '', message: '' });
  };

  const updateSearchParams = (key, value) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    setSearchParams(newParams);
  };

  return (
    <div className="page-container">
      <div className="content-section">
        <h1>Contact Us</h1>
        <p className="page-description">
          Get in touch with us. This page demonstrates form handling and URL search parameters.
        </p>

        <div className="search-params-demo">
          <h3>URL Search Parameters Demo</h3>
          <p><strong>Source:</strong> {source}</p>
          <p><strong>Reference:</strong> {ref}</p>
          
          <div className="param-controls">
            <button 
              onClick={() => updateSearchParams('source', 'newsletter')}
              className="param-button"
            >
              Set Source: Newsletter
            </button>
            <button 
              onClick={() => updateSearchParams('ref', 'homepage')}
              className="param-button"
            >
              Set Ref: Homepage
            </button>
            <button 
              onClick={() => setSearchParams({})}
              className="param-button clear"
            >
              Clear All Params
            </button>
          </div>
        </div>

        <div className="contact-form-container">
          <form onSubmit={handleSubmit} className="contact-form">
            <h3>Send us a Message</h3>
            
            <div className="form-group">
              <label htmlFor="name">Name:</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email:</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="message">Message:</label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                required
                rows="5"
                className="form-textarea"
              />
            </div>

            <button type="submit" className="submit-button">
              Send Message
            </button>
          </form>
        </div>

        <div className="contact-info">
          <h3>Other Ways to Reach Us</h3>
          <div className="contact-methods">
            <div className="contact-method">
              <strong>📧 Email:</strong> <EMAIL>
            </div>
            <div className="contact-method">
              <strong>📞 Phone:</strong> +****************
            </div>
            <div className="contact-method">
              <strong>📍 Address:</strong> 123 React Street, Router City, RC 12345
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
