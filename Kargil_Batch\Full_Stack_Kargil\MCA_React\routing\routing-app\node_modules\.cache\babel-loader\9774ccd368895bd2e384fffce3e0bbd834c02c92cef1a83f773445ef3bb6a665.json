{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kargil_Batch\\\\Full_Stack_Kargil\\\\MCA_React\\\\routing\\\\routing-app\\\\src\\\\components\\\\Services.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Services = () => {\n  _s();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Our Services\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"page-description\",\n        children: \"Explore our range of services. This page demonstrates various React Router features.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"location-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Current Location Info:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Pathname:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 14\n          }, this), \" \", location.pathname]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Search:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 14\n          }, this), \" \", location.search || 'No query parameters']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Hash:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 14\n          }, this), \" \", location.hash || 'No hash']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"services-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDF10 Web Development\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Modern web applications using React, Vue, and Angular.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services?category=web\",\n            className: \"service-link\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCF1 Mobile Development\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Cross-platform mobile apps using React Native and Flutter.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services?category=mobile\",\n            className: \"service-link\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u2601\\uFE0F Cloud Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Scalable cloud solutions and DevOps implementation.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services?category=cloud\",\n            className: \"service-link\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"service-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83E\\uDD16 AI & Machine Learning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Intelligent solutions powered by artificial intelligence.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services?category=ai\",\n            className: \"service-link\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"routing-demo\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Query Parameter Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Click the \\\"Learn More\\\" links above to see query parameters in action!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"demo-links\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services#web-section\",\n            className: \"demo-link\",\n            children: \"Jump to Web Section (Hash)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/services?filter=popular&sort=name\",\n            className: \"demo-link\",\n            children: \"Popular Services (Query Params)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(Services, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Services;\nexport default Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Services", "_s", "location", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pathname", "search", "hash", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Karg<PERSON>_<PERSON>ch/Full_Stack_Kargil/MCA_React/routing/routing-app/src/components/Services.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Services = () => {\n  const location = useLocation();\n\n  return (\n    <div className=\"page-container\">\n      <div className=\"content-section\">\n        <h1>Our Services</h1>\n        <p className=\"page-description\">\n          Explore our range of services. This page demonstrates various React Router features.\n        </p>\n\n        <div className=\"location-info\">\n          <h3>Current Location Info:</h3>\n          <p><strong>Pathname:</strong> {location.pathname}</p>\n          <p><strong>Search:</strong> {location.search || 'No query parameters'}</p>\n          <p><strong>Hash:</strong> {location.hash || 'No hash'}</p>\n        </div>\n\n        <div className=\"services-grid\">\n          <div className=\"service-card\">\n            <h3>🌐 Web Development</h3>\n            <p>Modern web applications using React, Vue, and Angular.</p>\n            <Link to=\"/services?category=web\" className=\"service-link\">\n              Learn More\n            </Link>\n          </div>\n\n          <div className=\"service-card\">\n            <h3>📱 Mobile Development</h3>\n            <p>Cross-platform mobile apps using React Native and Flutter.</p>\n            <Link to=\"/services?category=mobile\" className=\"service-link\">\n              Learn More\n            </Link>\n          </div>\n\n          <div className=\"service-card\">\n            <h3>☁️ Cloud Services</h3>\n            <p>Scalable cloud solutions and DevOps implementation.</p>\n            <Link to=\"/services?category=cloud\" className=\"service-link\">\n              Learn More\n            </Link>\n          </div>\n\n          <div className=\"service-card\">\n            <h3>🤖 AI & Machine Learning</h3>\n            <p>Intelligent solutions powered by artificial intelligence.</p>\n            <Link to=\"/services?category=ai\" className=\"service-link\">\n              Learn More\n            </Link>\n          </div>\n        </div>\n\n        <div className=\"routing-demo\">\n          <h3>Query Parameter Demo</h3>\n          <p>Click the \"Learn More\" links above to see query parameters in action!</p>\n          <div className=\"demo-links\">\n            <Link to=\"/services#web-section\" className=\"demo-link\">\n              Jump to Web Section (Hash)\n            </Link>\n            <Link to=\"/services?filter=popular&sort=name\" className=\"demo-link\">\n              Popular Services (Query Params)\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Services;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,oBACEE,OAAA;IAAKI,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BL,OAAA;MAAKI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BL,OAAA;QAAAK,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBT,OAAA;QAAGI,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAEhC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJT,OAAA;QAAKI,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BL,OAAA;UAAAK,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BT,OAAA;UAAAK,QAAA,gBAAGL,OAAA;YAAAK,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACN,QAAQ,CAACO,QAAQ;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDT,OAAA;UAAAK,QAAA,gBAAGL,OAAA;YAAAK,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACN,QAAQ,CAACQ,MAAM,IAAI,qBAAqB;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1ET,OAAA;UAAAK,QAAA,gBAAGL,OAAA;YAAAK,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACN,QAAQ,CAACS,IAAI,IAAI,SAAS;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAENT,OAAA;QAAKI,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BL,OAAA;UAAKI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BL,OAAA;YAAAK,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BT,OAAA;YAAAK,QAAA,EAAG;UAAsD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7DT,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,wBAAwB;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE3D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENT,OAAA;UAAKI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BL,OAAA;YAAAK,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BT,OAAA;YAAAK,QAAA,EAAG;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjET,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,2BAA2B;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENT,OAAA;UAAKI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BL,OAAA;YAAAK,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BT,OAAA;YAAAK,QAAA,EAAG;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1DT,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,0BAA0B;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENT,OAAA;UAAKI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BL,OAAA;YAAAK,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjCT,OAAA;YAAAK,QAAA,EAAG;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChET,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,uBAAuB;YAACT,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENT,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BL,OAAA;UAAAK,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BT,OAAA;UAAAK,QAAA,EAAG;QAAqE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5ET,OAAA;UAAKI,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBL,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,uBAAuB;YAACT,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPT,OAAA,CAACH,IAAI;YAACgB,EAAE,EAAC,oCAAoC;YAACT,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAnEID,QAAQ;EAAA,QACKH,WAAW;AAAA;AAAAgB,EAAA,GADxBb,QAAQ;AAqEd,eAAeA,QAAQ;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}