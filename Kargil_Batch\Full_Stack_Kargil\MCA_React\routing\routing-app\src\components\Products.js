import React from 'react';
import { Link } from 'react-router-dom';

const Products = () => {
  const categories = [
    { 
      name: 'electronics', 
      title: 'Electronics', 
      description: 'Smartphones, laptops, and gadgets',
      icon: '📱'
    },
    { 
      name: 'clothing', 
      title: 'Clothing', 
      description: 'Fashion and apparel',
      icon: '👕'
    },
    { 
      name: 'books', 
      title: 'Books', 
      description: 'Educational and programming books',
      icon: '📚'
    }
  ];

  return (
    <div className="page-container">
      <div className="content-section">
        <h1>Products</h1>
        <p className="page-description">
          Browse our product categories. This page demonstrates nested routing with useParams.
        </p>

        <div className="categories-grid">
          {categories.map(category => (
            <div key={category.name} className="category-card">
              <div className="category-icon">{category.icon}</div>
              <h3>{category.title}</h3>
              <p>{category.description}</p>
              <Link to={`/products/${category.name}`} className="category-button">
                Browse {category.title}
              </Link>
            </div>
          ))}
        </div>

        <div className="routing-demo">
          <h3>Dynamic Routing Examples</h3>
          <p>These links demonstrate different URL parameter scenarios:</p>
          
          <div className="demo-section">
            <h4>Valid Routes:</h4>
            <div className="demo-links">
              <Link to="/products/electronics" className="demo-link">
                /products/electronics (Category)
              </Link>
              <Link to="/products/electronics/1" className="demo-link">
                /products/electronics/1 (Product Details)
              </Link>
              <Link to="/user/1" className="demo-link">
                /user/1 (User Profile)
              </Link>
            </div>
          </div>

          <div className="demo-section">
            <h4>404 Scenarios (useParams validation):</h4>
            <div className="demo-links">
              <Link to="/products/invalid-category" className="demo-link error-link">
                /products/invalid-category (Invalid Category)
              </Link>
              <Link to="/products/electronics/999" className="demo-link error-link">
                /products/electronics/999 (Invalid Product)
              </Link>
              <Link to="/user/999" className="demo-link error-link">
                /user/999 (Invalid User)
              </Link>
            </div>
          </div>
        </div>

        <div className="useParams-explanation">
          <h3>How useParams Works with 404</h3>
          <div className="explanation-card">
            <h4>🔍 Parameter Validation Process:</h4>
            <ol>
              <li><strong>Extract Parameters:</strong> useParams() gets URL parameters</li>
              <li><strong>Validate Data:</strong> Check if parameters match expected values</li>
              <li><strong>Conditional Rendering:</strong> Show content or 404 based on validation</li>
              <li><strong>Custom Messages:</strong> Provide specific error messages</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Products;
