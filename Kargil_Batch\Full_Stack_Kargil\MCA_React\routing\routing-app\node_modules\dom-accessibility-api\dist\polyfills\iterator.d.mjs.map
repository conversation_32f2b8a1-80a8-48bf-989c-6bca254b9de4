{"version": 3, "file": "iterator.d.mjs", "names": [], "sources": ["../../sources/polyfills/iterator.d.ts"], "sourcesContent": ["// copied from https://github.com/microsoft/TypeScript/blob/eaeee9cc31bdc3a16f982a2e7b784573c977fdfa/lib/\n// but with `unknown` instead of `any`\ninterface IteratorYieldResult<TYield> {\n\tdone?: false;\n\tvalue: TYield;\n}\n\ninterface IteratorReturnResult<TReturn> {\n\tdone: true;\n\tvalue: TReturn;\n}\n\ntype IteratorResult<T, TReturn = unknown> =\n\t| IteratorYieldResult<T>\n\t| IteratorReturnResult<TReturn>;\n\ninterface Iterator<T, TReturn = unknown, TNext = undefined> {\n\t// NOTE: 'next' is defined using a tuple to ensure we report the correct assignability errors in all places.\n\tnext(...args: [] | [TNext]): IteratorResult<T, TReturn>;\n\treturn?(value?: TReturn): IteratorResult<T, TReturn>;\n\tthrow?(e?: unknown): IteratorResult<T, TReturn>;\n}\n\ninterface Iterable<T> {\n\t[Symbol.iterator](): Iterator<T>;\n}\n\ninterface IterableIterator<T> extends Iterator<T> {\n\t[Symbol.iterator](): IterableIterator<T>;\n}\ninterface SymbolConstructor {\n\t/**\n\t * A method that returns the default iterator for an object. Called by the semantics of the\n\t * for-of statement.\n\t */\n\treadonly iterator: symbol;\n}\n\ndeclare const Symbol: SymbolConstructor;\n"], "mappings": ""}