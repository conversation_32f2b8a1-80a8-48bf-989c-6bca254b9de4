import React from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import NotFound from './NotFound';

const ProductCategory = () => {
  const { category } = useParams();
  
  // Mock product data
  const products = {
    electronics: [
      { id: 1, name: 'Smartphone', price: '$699', description: 'Latest smartphone with advanced features' },
      { id: 2, name: 'Laptop', price: '$1299', description: 'High-performance laptop for professionals' },
      { id: 3, name: 'Headphones', price: '$199', description: 'Wireless noise-canceling headphones' }
    ],
    clothing: [
      { id: 1, name: 'T-Shirt', price: '$29', description: 'Comfortable cotton t-shirt' },
      { id: 2, name: '<PERSON><PERSON>', price: '$79', description: 'Classic blue jeans' },
      { id: 3, name: 'Sneakers', price: '$129', description: 'Comfortable running sneakers' }
    ],
    books: [
      { id: 1, name: 'React Guide', price: '$39', description: 'Complete guide to React development' },
      { id: 2, name: 'JavaScript Mastery', price: '$45', description: 'Master JavaScript programming' },
      { id: 3, name: 'Web Design', price: '$35', description: 'Modern web design principles' }
    ]
  };

  const validCategories = Object.keys(products);
  
  // Check if category exists - if not, render 404
  if (!validCategories.includes(category)) {
    return <NotFound customMessage={`Category "${category}" not found`} />;
  }

  const categoryProducts = products[category];

  return (
    <div className="page-container">
      <div className="content-section">
        <div className="breadcrumb">
          <Link to="/products" className="breadcrumb-link">Products</Link>
          <span className="breadcrumb-separator">›</span>
          <span className="breadcrumb-current">{category}</span>
        </div>

        <h1>{category.charAt(0).toUpperCase() + category.slice(1)} Products</h1>
        <p className="page-description">
          Browse our collection of {category} products. Category parameter: <strong>{category}</strong>
        </p>

        <div className="products-grid">
          {categoryProducts.map(product => (
            <div key={product.id} className="product-card-small">
              <div className="product-image-small">
                📦
              </div>
              <div className="product-info-small">
                <h3>{product.name}</h3>
                <p className="product-price">{product.price}</p>
                <p className="product-description-small">{product.description}</p>
                <Link 
                  to={`/products/${category}/${product.id}`} 
                  className="view-product-btn"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="category-navigation">
          <h3>Browse Other Categories</h3>
          <div className="category-links">
            {validCategories.map(cat => (
              <Link 
                key={cat} 
                to={`/products/${cat}`} 
                className={`category-link ${cat === category ? 'active' : ''}`}
              >
                {cat.charAt(0).toUpperCase() + cat.slice(1)}
              </Link>
            ))}
            <Link to="/products/invalid" className="category-link error-link">
              Invalid Category (404)
            </Link>
          </div>
        </div>

        <div className="params-info">
          <h3>useParams() Information</h3>
          <div className="params-display">
            <p><strong>Category parameter:</strong> {category}</p>
            <p><strong>URL pattern:</strong> /products/:category</p>
            <p><strong>Valid categories:</strong> {validCategories.join(', ')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCategory;
