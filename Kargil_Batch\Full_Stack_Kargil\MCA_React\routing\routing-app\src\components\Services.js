import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Services = () => {
  const location = useLocation();

  return (
    <div className="page-container">
      <div className="content-section">
        <h1>Our Services</h1>
        <p className="page-description">
          Explore our range of services. This page demonstrates various React Router features.
        </p>

        <div className="location-info">
          <h3>Current Location Info:</h3>
          <p><strong>Pathname:</strong> {location.pathname}</p>
          <p><strong>Search:</strong> {location.search || 'No query parameters'}</p>
          <p><strong>Hash:</strong> {location.hash || 'No hash'}</p>
        </div>

        <div className="services-grid">
          <div className="service-card">
            <h3>🌐 Web Development</h3>
            <p>Modern web applications using React, Vue, and Angular.</p>
            <Link to="/services?category=web" className="service-link">
              Learn More
            </Link>
          </div>

          <div className="service-card">
            <h3>📱 Mobile Development</h3>
            <p>Cross-platform mobile apps using React Native and Flutter.</p>
            <Link to="/services?category=mobile" className="service-link">
              Learn More
            </Link>
          </div>

          <div className="service-card">
            <h3>☁️ Cloud Services</h3>
            <p>Scalable cloud solutions and DevOps implementation.</p>
            <Link to="/services?category=cloud" className="service-link">
              Learn More
            </Link>
          </div>

          <div className="service-card">
            <h3>🤖 AI & Machine Learning</h3>
            <p>Intelligent solutions powered by artificial intelligence.</p>
            <Link to="/services?category=ai" className="service-link">
              Learn More
            </Link>
          </div>
        </div>

        <div className="routing-demo">
          <h3>Query Parameter Demo</h3>
          <p>Click the "Learn More" links above to see query parameters in action!</p>
          <div className="demo-links">
            <Link to="/services#web-section" className="demo-link">
              Jump to Web Section (Hash)
            </Link>
            <Link to="/services?filter=popular&sort=name" className="demo-link">
              Popular Services (Query Params)
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Services;
