import React from 'react';

const Home = () => {
  return (
    <div className="page-container">
      <div className="hero-section">
        <h1>Welcome to React Router Demo</h1>
        <p className="hero-text">
          This is a demonstration of browser routing in React using React Router DOM.
          Navigate through different pages using the navigation menu above.
        </p>
        <div className="features">
          <div className="feature-card">
            <h3>🏠 Home Page</h3>
            <p>You are currently on the home page. This is the default route.</p>
          </div>
          <div className="feature-card">
            <h3>🧭 Browser Routing</h3>
            <p>Notice how the URL changes as you navigate between pages without page refresh.</p>
          </div>
          <div className="feature-card">
            <h3>📱 Single Page Application</h3>
            <p>All routing is handled client-side for a smooth user experience.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
